import { useState } from 'react';

const AssessmentQuestion = ({ 
  question, 
  questionIndex, 
  scale, 
  value, 
  onChange, 
  isReverse = false 
}) => {
  return (
    <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 mb-4">
      <div className="mb-4">
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          Question {questionIndex + 1}
        </h3>
        <p className="text-gray-700">
          {isReverse && <span className="text-sm text-red-600 font-medium">(Reverse scored) </span>}
          {question}
        </p>
      </div>
      
      <div className="space-y-2">
        {scale.map((option) => (
          <label key={option.value} className="flex items-center">
            <input
              type="radio"
              name={`question-${questionIndex}`}
              value={option.value}
              checked={value === option.value}
              onChange={(e) => onChange(parseInt(e.target.value))}
              className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
            />
            <span className="ml-3 text-sm text-gray-700">
              {option.value} - {option.label}
            </span>
          </label>
        ))}
      </div>
    </div>
  );
};

export default AssessmentQuestion;
