# ATMA Notification Service

Real-time notification service menggunakan WebSocket untuk mengirim notifikasi analisis ke client.

## Features

- **Real-time WebSocket**: Socket.IO untuk komunikasi real-time
- **JWT Authentication**: Autentikasi client menggunakan JWT token
- **Internal API**: HTTP endpoints untuk menerima notifikasi dari analysis-worker
- **Connection Management**: Manajemen koneksi user dengan multiple sessions
- **Logging**: Comprehensive logging untuk monitoring

## Environment Variables

```env
PORT=3005
JWT_SECRET=your_jwt_secret
INTERNAL_SERVICE_KEY=your_service_key
CORS_ORIGIN=http://localhost:3000
LOG_LEVEL=info
```

## WebSocket Events

### Client to Server
- `authenticate`: Autentikasi dengan JWT token

### Server to Client
- `authenticated`: Konfirmasi autentikasi berhasil
- `auth_error`: Error autentikasi
- `analysis-complete`: Notifikasi analisis selesai
- `analysis-failed`: Notifikasi analisis gagal

## Internal API Endpoints

### POST /notifications/analysis-complete
Menerima notifikasi dari analysis-worker ketika analisis selesai.

**Headers:**
```
X-Internal-Service: true
X-Service-Key: your_service_key
```

**Body:**
```json
{
  "userId": "uuid",
  "jobId": "uuid",
  "resultId": "uuid", 
  "status": "completed",
  "message": "Your analysis is ready!"
}
```

### POST /notifications/analysis-failed
Menerima notifikasi ketika analisis gagal.

## Cara Penggunaan untuk Client

### 1. Instalasi Dependencies

Client perlu menginstall Socket.IO client:

```bash
npm install socket.io-client
```

### 2. Koneksi ke Notification Service

```javascript
import io from 'socket.io-client';

// Buat koneksi ke notification service
const socket = io('http://localhost:3005', {
  autoConnect: false // Optional: manual connection control
});
```

### 3. Autentikasi

Setelah koneksi terbentuk, client **WAJIB** melakukan autentikasi menggunakan JWT token:

```javascript
// Connect dan authenticate
socket.connect();
socket.emit('authenticate', { token: 'your_jwt_token' });
```

### 4. Event Handling

Client dapat mendengarkan berbagai event dari server:

```javascript
// Event autentikasi
socket.on('authenticated', () => {
  console.log('✅ Successfully authenticated');
});

socket.on('auth_error', (error) => {
  console.error('❌ Authentication failed:', error);
  // Handle authentication failure (redirect to login, etc.)
});

// Event notifikasi analisis
socket.on('analysis-complete', (data) => {
  console.log('🎉 Analysis complete:', data);
  // data berisi: { userId, jobId, resultId, status, message }
  // Implementasi: show notification, update UI, redirect ke hasil, etc.
});

socket.on('analysis-failed', (data) => {
  console.log('💥 Analysis failed:', data);
  // data berisi: { userId, jobId, error, message }
  // Implementasi: show error notification, retry option, etc.
});

// Event koneksi
socket.on('connect', () => {
  console.log('🔗 Connected to notification service');
});

socket.on('disconnect', () => {
  console.log('🔌 Disconnected from notification service');
});
```

### 5. Implementasi Lengkap

```javascript
import io from 'socket.io-client';

class NotificationClient {
  constructor(jwtToken, options = {}) {
    this.token = jwtToken;
    this.socket = io(options.url || 'http://localhost:3005', {
      autoConnect: false,
      ...options.socketOptions
    });

    this.isAuthenticated = false;
    this.setupEventListeners();
  }

  connect() {
    this.socket.connect();
    return this;
  }

  disconnect() {
    this.socket.disconnect();
    return this;
  }

  authenticate() {
    if (this.socket.connected) {
      this.socket.emit('authenticate', { token: this.token });
    }
  }

  setupEventListeners() {
    // Connection events
    this.socket.on('connect', () => {
      console.log('Connected to notification service');
      this.authenticate();
    });

    this.socket.on('disconnect', () => {
      console.log('Disconnected from notification service');
      this.isAuthenticated = false;
    });

    // Authentication events
    this.socket.on('authenticated', () => {
      console.log('Successfully authenticated');
      this.isAuthenticated = true;
      this.onAuthenticated?.();
    });

    this.socket.on('auth_error', (error) => {
      console.error('Authentication failed:', error);
      this.isAuthenticated = false;
      this.onAuthError?.(error);
    });

    // Notification events
    this.socket.on('analysis-complete', (data) => {
      this.onAnalysisComplete?.(data);
    });

    this.socket.on('analysis-failed', (data) => {
      this.onAnalysisFailed?.(data);
    });
  }

  // Callback setters untuk custom handling
  onAuthenticated(callback) {
    this.onAuthenticated = callback;
    return this;
  }

  onAuthError(callback) {
    this.onAuthError = callback;
    return this;
  }

  onAnalysisComplete(callback) {
    this.onAnalysisComplete = callback;
    return this;
  }

  onAnalysisFailed(callback) {
    this.onAnalysisFailed = callback;
    return this;
  }
}

// Contoh penggunaan
const notificationClient = new NotificationClient('your_jwt_token')
  .onAuthenticated(() => {
    console.log('Ready to receive notifications!');
  })
  .onAuthError((error) => {
    // Redirect ke login atau refresh token
    window.location.href = '/login';
  })
  .onAnalysisComplete((data) => {
    // Show success notification
    showNotification('Analysis completed!', 'success');
    // Redirect ke hasil analisis
    window.location.href = `/results/${data.resultId}`;
  })
  .onAnalysisFailed((data) => {
    // Show error notification
    showNotification('Analysis failed: ' + data.message, 'error');
  })
  .connect();
```

### 6. Best Practices

#### Reconnection Handling
```javascript
const socket = io('http://localhost:3005', {
  reconnection: true,
  reconnectionDelay: 1000,
  reconnectionAttempts: 5,
  maxReconnectionAttempts: 5
});
```

#### Error Handling
```javascript
socket.on('connect_error', (error) => {
  console.error('Connection failed:', error);
  // Implement fallback mechanism
});

socket.on('reconnect_failed', () => {
  console.error('Failed to reconnect after maximum attempts');
  // Show offline notification to user
});
```

#### Token Refresh
```javascript
// Jika token expired, refresh dan authenticate ulang
socket.on('auth_error', async (error) => {
  if (error.code === 'TOKEN_EXPIRED') {
    try {
      const newToken = await refreshJWTToken();
      socket.emit('authenticate', { token: newToken });
    } catch (refreshError) {
      // Redirect to login
      window.location.href = '/login';
    }
  }
});
```

### 7. Integration dengan React/Vue

#### React Hook Example
```javascript
import { useEffect, useState } from 'react';
import { NotificationClient } from './NotificationClient';

export function useNotifications(token) {
  const [client, setClient] = useState(null);
  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    if (!token) return;

    const notificationClient = new NotificationClient(token)
      .onAuthenticated(() => setIsConnected(true))
      .onAuthError(() => setIsConnected(false))
      .connect();

    setClient(notificationClient);

    return () => {
      notificationClient.disconnect();
    };
  }, [token]);

  return { client, isConnected };
}
```

### 8. Format Data Notifikasi

#### Analysis Complete Event
```javascript
{
  "userId": "uuid-string",
  "jobId": "uuid-string",
  "resultId": "uuid-string",
  "status": "completed",
  "message": "Your analysis is ready!",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

#### Analysis Failed Event
```javascript
{
  "userId": "uuid-string",
  "jobId": "uuid-string",
  "error": "error-code",
  "message": "Analysis failed: Invalid file format",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### 9. Troubleshooting

#### Connection Issues
```javascript
// Check if service is running
socket.on('connect_error', (error) => {
  console.error('Cannot connect to notification service:', error);
  // Pastikan service berjalan di port 3005
});
```

#### Authentication Issues
```javascript
socket.on('auth_error', (error) => {
  console.error('Auth error:', error);
  // Periksa:
  // 1. JWT token valid dan belum expired
  // 2. JWT_SECRET sama dengan yang di service
  // 3. Format token benar
});
```

#### CORS Issues
Pastikan `CORS_ORIGIN` di environment variables sesuai dengan domain client:
```env
CORS_ORIGIN=http://localhost:3000
```

#### Multiple Connections
Service mendukung multiple sessions per user. Jika user membuka multiple tabs/windows, semua akan menerima notifikasi yang sama.

### 10. Testing

#### Manual Testing dengan Browser Console
```javascript
// Di browser console
const socket = io('http://localhost:3005');
socket.emit('authenticate', { token: 'your_jwt_token' });
socket.on('authenticated', () => console.log('Auth OK'));
socket.on('analysis-complete', (data) => console.log('Received:', data));
```

#### Unit Testing
```javascript
// Contoh test dengan Jest dan socket.io-client
import io from 'socket.io-client';

describe('Notification Client', () => {
  let socket;

  beforeEach(() => {
    socket = io('http://localhost:3005', { autoConnect: false });
  });

  afterEach(() => {
    socket.disconnect();
  });

  test('should authenticate successfully', (done) => {
    socket.connect();
    socket.emit('authenticate', { token: 'valid_token' });

    socket.on('authenticated', () => {
      expect(true).toBe(true);
      done();
    });
  });
});
```

## Running the Service

### Quick Start
```bash
# Install dependencies
npm install

# Run tests
npm test

# Start in development mode
npm run dev
```

### Using Start Script (Recommended)
```bash
# Windows PowerShell
powershell -ExecutionPolicy Bypass -File scripts/start-service.ps1

# This script will:
# - Check dependencies and install if needed
# - Check for vulnerabilities and fix them
# - Run all tests
# - Start the service in development mode
```

### Production
```bash
npm start
```

## Testing

### Automated Tests
```bash
# Run unit tests
npm test

# Run tests in watch mode
npm run test:watch
```

### Manual Testing
```bash
# Test all endpoints (service must be running)
powershell -ExecutionPolicy Bypass -File scripts/test-service.ps1
```

### Test Results Summary
✅ **All tests passing:**
- Health check endpoint
- Service authentication
- Analysis complete notifications
- Analysis failed notifications
- 404 error handling
- Security vulnerabilities fixed

## Monitoring

Service menyediakan logging untuk monitoring koneksi dan notifikasi:

```bash
# Lihat logs
npm run logs

# Atau jika menggunakan PM2
pm2 logs notification-service
```

## Service Status

### Health Check
- **Endpoint**: `GET /health`
- **Status**: ✅ Operational
- **Response**: Service status and connection count

### Security
- **Vulnerabilities**: ✅ 0 high severity issues
- **Authentication**: ✅ JWT + Internal service key
- **CORS**: ✅ Configured
- **Headers**: ✅ Security headers (Helmet)

### Performance
- **WebSocket**: ✅ Socket.IO with reconnection
- **Connection Management**: ✅ User session tracking
- **Error Handling**: ✅ Comprehensive error responses