// API Configuration
export const API_CONFIG = {
  // API Gateway URL
  BASE_URL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000',
  
  // Notification Service URL
  NOTIFICATION_URL: import.meta.env.VITE_NOTIFICATION_URL || 'http://localhost:3005',
  
  // Request timeout
  TIMEOUT: 30000,
  
  // Retry configuration
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
};

// API Endpoints
export const API_ENDPOINTS = {
  // Auth endpoints
  AUTH: {
    LOGIN: '/auth/login',
    REGISTER: '/auth/register',
    PROFILE: '/auth/profile',
    LOGOUT: '/auth/logout',
    TOKEN_BALANCE: '/auth/token-balance',
  },
  
  // Assessment endpoints
  ASSESSMENT: {
    SUBMIT: '/assessments/submit',
    STATUS: (jobId) => `/assessments/status/${jobId}`,
  },
  
  // Archive endpoints
  ARCHIVE: {
    RESULTS: '/archive/results',
    RESULT_BY_ID: (id) => `/archive/results/${id}`,
    STATS: '/archive/stats',
    STATS_SUMMARY: '/archive/stats/summary',
  },
  
  // Health check
  HEALTH: '/health',
};

export default API_CONFIG;
