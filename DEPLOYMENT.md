# ATMA Frontend - Deployment Guide

## 🚀 Quick Start

### Prerequisites
- Node.js 16+
- npm or yarn
- Backend API Gateway running on port 3000
- Notification Service running on port 3005

### Installation & Setup

1. **Install Dependencies**
   ```bash
   npm install
   ```

2. **Environment Configuration**
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` file:
   ```env
   VITE_API_BASE_URL=http://localhost:3000
   VITE_NOTIFICATION_URL=http://localhost:3005
   VITE_APP_ENV=development
   VITE_DEBUG=true
   ```

3. **Start Development Server**
   ```bash
   npm run dev
   ```
   
   Application will be available at: `http://localhost:5173`

## 🔧 Build & Production

### Build for Production
```bash
npm run build
```

### Preview Production Build
```bash
npm run preview
```

### Production Environment Variables
```env
VITE_API_BASE_URL=https://your-api-domain.com
VITE_NOTIFICATION_URL=https://your-notification-domain.com
VITE_APP_ENV=production
VITE_DEBUG=false
```

## 🐛 Troubleshooting

### Common Issues

1. **PostCSS/Tailwind Error**
   ```bash
   npm install @tailwindcss/postcss
   ```

2. **Dependencies Issues**
   ```bash
   rm -rf node_modules package-lock.json
   npm install
   ```

3. **Port Already in Use**
   - Vite will automatically try another port
   - Or specify custom port: `npm run dev -- --port 3001`

### Backend Connection Issues

1. **API Gateway Not Running**
   - Ensure backend API Gateway is running on port 3000
   - Check CORS configuration

2. **Notification Service Not Running**
   - Ensure notification service is running on port 3005
   - WebSocket will fallback to polling if unavailable

## 📱 Testing

### Manual Testing Flow

1. **Authentication**
   - Register new user
   - Login with credentials
   - Verify JWT token storage

2. **Assessment Flow**
   - Start new assessment
   - Complete VIA Character Strengths
   - Complete RIASEC Holland Codes
   - Complete Big Five Inventory
   - Submit assessment

3. **Status Monitoring**
   - Verify status page shows job ID
   - Check WebSocket connection status
   - Monitor progress updates

4. **Results Display**
   - Verify results page loads
   - Check persona profiles
   - Verify detailed scores

### Browser Compatibility

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## 🔒 Security Considerations

- JWT tokens stored in localStorage
- Automatic token refresh on 401 errors
- CORS configuration required for backend
- HTTPS recommended for production

## 📊 Performance

- Code splitting with React Router
- Lazy loading components
- Optimized bundle size with Vite
- Efficient state management with Context API

## 🌐 Deployment Options

### Static Hosting (Recommended)
- Vercel
- Netlify
- GitHub Pages
- AWS S3 + CloudFront

### Server Deployment
- Docker container
- Node.js server
- Nginx reverse proxy

### Docker Deployment
```dockerfile
FROM node:16-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "run", "preview"]
```

## 📈 Monitoring

### Development
- Browser DevTools
- React DevTools
- Network tab for API calls
- Console for WebSocket events

### Production
- Error tracking (Sentry)
- Analytics (Google Analytics)
- Performance monitoring
- User feedback collection

## 🔄 Updates & Maintenance

### Dependency Updates
```bash
npm audit
npm update
```

### Security Updates
```bash
npm audit fix
```

### Version Management
- Follow semantic versioning
- Tag releases in Git
- Maintain changelog

## 📞 Support

For issues and questions:
- Check browser console for errors
- Verify backend services are running
- Review network requests in DevTools
- Check WebSocket connection status
