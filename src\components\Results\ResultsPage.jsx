import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import axios from 'axios';

const ResultsPage = () => {
  const { resultId } = useParams();
  const navigate = useNavigate();
  const [result, setResult] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchResult = async () => {
      try {
        const response = await axios.get(`/archive/results/${resultId}`);
        
        if (response.data.success) {
          setResult(response.data.data);
        }
      } catch (err) {
        setError(err.response?.data?.message || 'Failed to load results');
      } finally {
        setIsLoading(false);
      }
    };

    if (resultId) {
      fetchResult();
    } else {
      navigate('/dashboard');
    }
  }, [resultId, navigate]);

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const renderPersonaProfile = (personaProfile) => {
    if (!personaProfile || !Array.isArray(personaProfile)) {
      return <p className="text-gray-600">No persona profile available.</p>;
    }

    return (
      <div className="space-y-6">
        {personaProfile.map((persona, index) => (
          <div key={index} className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">
              {persona.title || `Persona ${index + 1}`}
            </h3>
            
            {persona.description && (
              <p className="text-gray-700 mb-4">{persona.description}</p>
            )}
            
            {persona.strengths && (
              <div className="mb-4">
                <h4 className="text-lg font-medium text-gray-800 mb-2">Strengths</h4>
                <ul className="list-disc list-inside space-y-1">
                  {persona.strengths.map((strength, idx) => (
                    <li key={idx} className="text-gray-700">{strength}</li>
                  ))}
                </ul>
              </div>
            )}
            
            {persona.recommendations && (
              <div className="mb-4">
                <h4 className="text-lg font-medium text-gray-800 mb-2">Recommendations</h4>
                <ul className="list-disc list-inside space-y-1">
                  {persona.recommendations.map((rec, idx) => (
                    <li key={idx} className="text-gray-700">{rec}</li>
                  ))}
                </ul>
              </div>
            )}
            
            {persona.careerPaths && (
              <div>
                <h4 className="text-lg font-medium text-gray-800 mb-2">Career Paths</h4>
                <ul className="list-disc list-inside space-y-1">
                  {persona.careerPaths.map((path, idx) => (
                    <li key={idx} className="text-gray-700">{path}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        ))}
      </div>
    );
  };

  const renderAssessmentData = (assessmentData) => {
    if (!assessmentData) return null;

    return (
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <h3 className="text-xl font-semibold text-gray-900 mb-4">Assessment Scores</h3>
        
        {assessmentData.riasec && (
          <div className="mb-6">
            <h4 className="text-lg font-medium text-gray-800 mb-3">RIASEC Holland Codes</h4>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              {Object.entries(assessmentData.riasec).map(([key, value]) => (
                <div key={key} className="text-center">
                  <div className="text-2xl font-bold text-indigo-600">{value}</div>
                  <div className="text-sm text-gray-600 capitalize">{key}</div>
                </div>
              ))}
            </div>
          </div>
        )}
        
        {assessmentData.ocean && (
          <div className="mb-6">
            <h4 className="text-lg font-medium text-gray-800 mb-3">Big Five (OCEAN)</h4>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              {Object.entries(assessmentData.ocean).map(([key, value]) => (
                <div key={key} className="text-center">
                  <div className="text-2xl font-bold text-green-600">{value}</div>
                  <div className="text-sm text-gray-600 capitalize">{key}</div>
                </div>
              ))}
            </div>
          </div>
        )}
        
        {assessmentData.viaIs && (
          <div>
            <h4 className="text-lg font-medium text-gray-800 mb-3">VIA Character Strengths</h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              {Object.entries(assessmentData.viaIs).map(([key, value]) => (
                <div key={key} className="text-center">
                  <div className="text-lg font-bold text-purple-600">{value}</div>
                  <div className="text-xs text-gray-600 capitalize">{key.replace(/([A-Z])/g, ' $1').trim()}</div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600 mx-auto"></div>
          <h2 className="mt-4 text-xl font-semibold text-gray-900">Loading Results...</h2>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="h-16 w-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="h-8 w-8 text-red-600" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </div>
          <h2 className="text-lg font-medium text-red-900 mb-2">Error Loading Results</h2>
          <p className="text-red-700 mb-4">{error}</p>
          <button
            onClick={() => navigate('/dashboard')}
            className="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700"
          >
            Back to Dashboard
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex justify-between items-center">
            <h1 className="text-3xl font-bold text-gray-900">Assessment Results</h1>
            <button
              onClick={() => navigate('/dashboard')}
              className="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700"
            >
              Back to Dashboard
            </button>
          </div>
          
          {result && (
            <div className="mt-4 text-sm text-gray-600">
              <p>Completed: {formatDate(result.created_at)}</p>
              <p>Status: <span className="capitalize font-medium">{result.status}</span></p>
            </div>
          )}
        </div>

        {result && (
          <div className="space-y-8">
            {/* Persona Profile */}
            <div>
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Your Talent Profile</h2>
              {renderPersonaProfile(result.persona_profile)}
            </div>

            {/* Assessment Data */}
            <div>
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Detailed Scores</h2>
              {renderAssessmentData(result.assessment_data)}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ResultsPage;
