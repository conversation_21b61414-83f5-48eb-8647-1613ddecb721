# ATMA Frontend - Testing Guide

## 🧪 Manual Testing Checklist

### Authentication Flow
- [ ] Register new user with valid email/password
- [ ] Register with invalid email format (should show error)
- [ ] Register with weak password (should show error)
- [ ] Login with valid credentials
- [ ] Login with invalid credentials (should show error)
- [ ] JWT token stored in localStorage
- [ ] Automatic redirect to dashboard after login
- [ ] Logout functionality clears token

### Assessment Flow
- [ ] Start new assessment from dashboard
- [ ] VIA Character Strengths assessment (96 questions)
  - [ ] Question pagination works
  - [ ] Progress bar updates correctly
  - [ ] Cannot proceed without answering questions
  - [ ] Can navigate back to previous pages
- [ ] RIASEC Holland Codes assessment (60 questions)
  - [ ] Smooth transition from VIA
  - [ ] All questions display correctly
  - [ ] Scoring calculation works
- [ ] Big Five Inventory assessment (44 questions)
  - [ ] Reverse-scored questions marked correctly
  - [ ] Final submission button appears
  - [ ] Cannot submit incomplete assessment

### Status Monitoring
- [ ] Redirect to status page after submission
- [ ] Job ID displayed correctly
- [ ] WebSocket connection status shown
- [ ] Progress updates (if backend supports)
- [ ] Polling fallback works when WebSocket unavailable
- [ ] Automatic redirect to results when complete

### Results Display
- [ ] Results page loads with assessment data
- [ ] Persona profiles display correctly
- [ ] Detailed scores show all categories
- [ ] RIASEC scores (6 categories)
- [ ] Big Five scores (5 categories)
- [ ] VIA Character Strengths scores (24 categories)
- [ ] Navigation back to dashboard works

### Dashboard
- [ ] Assessment history displays
- [ ] User statistics show correctly
- [ ] Token balance visible
- [ ] New assessment button works
- [ ] View results links work for completed assessments

### UI/UX
- [ ] Responsive design on mobile devices
- [ ] Loading spinners show during API calls
- [ ] Error messages display appropriately
- [ ] Toast notifications work
- [ ] Progress bars animate smoothly
- [ ] Connection status indicator updates

### Error Handling
- [ ] Network errors handled gracefully
- [ ] Invalid API responses handled
- [ ] Token expiration triggers re-login
- [ ] WebSocket connection failures handled
- [ ] Form validation errors display

## 🔧 Development Testing

### Browser DevTools
1. **Console Tab**
   - Check for JavaScript errors
   - Monitor WebSocket events
   - Verify API request/response logs

2. **Network Tab**
   - Verify API calls to correct endpoints
   - Check request headers include JWT token
   - Monitor WebSocket connection

3. **Application Tab**
   - Verify localStorage contains token and user data
   - Check session storage if used

### React DevTools
- Component state inspection
- Context provider values
- Performance profiling

## 🌐 Cross-Browser Testing

### Desktop Browsers
- [ ] Chrome (latest)
- [ ] Firefox (latest)
- [ ] Safari (latest)
- [ ] Edge (latest)

### Mobile Browsers
- [ ] Chrome Mobile
- [ ] Safari Mobile
- [ ] Firefox Mobile

## 📱 Responsive Testing

### Screen Sizes
- [ ] Mobile (320px - 768px)
- [ ] Tablet (768px - 1024px)
- [ ] Desktop (1024px+)

### Orientation
- [ ] Portrait mode
- [ ] Landscape mode

## 🔒 Security Testing

### Authentication
- [ ] JWT token validation
- [ ] Automatic logout on token expiration
- [ ] Protected routes redirect to login
- [ ] No sensitive data in localStorage

### API Security
- [ ] CORS headers configured
- [ ] Request headers include proper authentication
- [ ] No API keys exposed in frontend code

## ⚡ Performance Testing

### Load Times
- [ ] Initial page load < 3 seconds
- [ ] Assessment pages load quickly
- [ ] Image optimization
- [ ] Bundle size optimization

### Memory Usage
- [ ] No memory leaks in long sessions
- [ ] WebSocket connections properly cleaned up
- [ ] Component unmounting works correctly

## 🐛 Common Issues & Solutions

### WebSocket Connection Issues
```javascript
// Check connection status
console.log(notificationService.getConnectionStatus());

// Manual reconnection
notificationService.disconnect();
notificationService.connect(token);
```

### API Connection Issues
```javascript
// Check axios configuration
console.log(axios.defaults.baseURL);
console.log(axios.defaults.headers.common);

// Test API endpoint
axios.get('/health').then(console.log).catch(console.error);
```

### State Management Issues
```javascript
// Check auth context
const { user, token, isAuthenticated } = useAuth();
console.log({ user, token, isAuthenticated });
```

## 📊 Test Data

### Sample User Credentials
```javascript
// Test user (if backend supports)
{
  email: "<EMAIL>",
  password: "password123"
}
```

### Mock Assessment Data
```javascript
// Use test data utilities
import { enableTestMode, mockAssessmentResult } from './utils/testData';
enableTestMode();
```

## 🚀 Automated Testing Setup

### Unit Tests (Future Enhancement)
```bash
npm install --save-dev @testing-library/react @testing-library/jest-dom vitest
```

### E2E Tests (Future Enhancement)
```bash
npm install --save-dev cypress
```

## 📈 Performance Monitoring

### Lighthouse Audit
- Run Lighthouse in Chrome DevTools
- Check Performance, Accessibility, Best Practices, SEO scores
- Target: 90+ in all categories

### Bundle Analysis
```bash
npm run build
npx vite-bundle-analyzer dist
```

## 🔄 Continuous Testing

### Pre-deployment Checklist
- [ ] All manual tests pass
- [ ] No console errors
- [ ] Responsive design verified
- [ ] Performance metrics acceptable
- [ ] Security checks completed

### Post-deployment Verification
- [ ] Production URL accessible
- [ ] API connections working
- [ ] WebSocket connections stable
- [ ] User registration/login functional
