import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import AssessmentForm from './AssessmentForm';
import LoadingSpinner from '../UI/LoadingSpinner';
import { viaQuestions, riasecQuestions, bigFiveQuestions } from '../../data/assessmentQuestions';

const AssessmentFlow = () => {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(1);
  const [assessmentResults, setAssessmentResults] = useState({
    via: null,
    riasec: null,
    bigFive: null
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');

  const assessments = [
    { key: 'via', data: viaQuestions, title: 'VIA Character Strengths' },
    { key: 'riasec', data: riasecQuestions, title: 'RIASEC Holland Codes' },
    { key: 'bigFive', data: bigFiveQuestions, title: 'Big Five Personality' }
  ];

  const currentAssessment = assessments[currentStep - 1];

  const handleAssessmentComplete = (scores) => {
    setAssessmentResults(prev => ({
      ...prev,
      [currentAssessment.key]: scores
    }));
  };

  const handleNext = () => {
    if (currentStep < assessments.length) {
      setCurrentStep(prev => prev + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(prev => prev - 1);
    }
  };

  const transformScoresForAPI = () => {
    const { via, riasec, bigFive } = assessmentResults;
    
    // Transform VIA scores to match API format
    const viaIs = {};
    if (via) {
      Object.entries(via).forEach(([key, value]) => {
        viaIs[key] = value;
      });
    }

    // Transform RIASEC scores to match API format
    const riasecScores = {};
    if (riasec) {
      riasecScores.realistic = riasec.realistic || 0;
      riasecScores.investigative = riasec.investigative || 0;
      riasecScores.artistic = riasec.artistic || 0;
      riasecScores.social = riasec.social || 0;
      riasecScores.enterprising = riasec.enterprising || 0;
      riasecScores.conventional = riasec.conventional || 0;
    }

    // Transform Big Five scores to match API format (OCEAN)
    const ocean = {};
    if (bigFive) {
      ocean.openness = bigFive.openness || 0;
      ocean.conscientiousness = bigFive.conscientiousness || 0;
      ocean.extraversion = bigFive.extraversion || 0;
      ocean.agreeableness = bigFive.agreeableness || 0;
      ocean.neuroticism = bigFive.neuroticism || 0;
    }

    return {
      riasec: riasecScores,
      ocean: ocean,
      viaIs: viaIs
    };
  };

  const handleSubmitAll = async () => {
    setIsSubmitting(true);
    setError('');

    try {
      const transformedData = transformScoresForAPI();
      
      const response = await axios.post('/assessments/submit', transformedData);
      
      if (response.data.success) {
        const { jobId } = response.data.data;
        // Navigate to status page with jobId
        navigate(`/assessment/status/${jobId}`);
      }
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to submit assessment');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isSubmitting) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <LoadingSpinner
          size="xl"
          text="Submitting Assessment... Please wait while we process your responses."
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4 m-4">
          <div className="flex">
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error</h3>
              <div className="mt-2 text-sm text-red-700">{error}</div>
            </div>
          </div>
        </div>
      )}

      <AssessmentForm
        assessmentData={currentAssessment.data}
        onSubmit={currentStep === assessments.length ? handleSubmitAll : handleAssessmentComplete}
        onNext={handleNext}
        onPrevious={handlePrevious}
        isLastAssessment={currentStep === assessments.length}
        currentStep={currentStep}
        totalSteps={assessments.length}
      />
    </div>
  );
};

export default AssessmentFlow;
